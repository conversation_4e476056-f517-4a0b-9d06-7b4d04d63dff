// User Details interface matching backend UserDetails model
export interface UserDetails {
  id?: string;
  fullName?: string;
  email?: string;
  isActive: boolean;
  profileImageUrl?: string;
  roles: string[];
  phoneNumber?: string;
  description?: string;
  createdOn: Date;
}

export interface PaginationParameters {
  pageNumber: number;
  pageSize: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  name?: string;
  role?: string;
  status?: string;
  createdFrom?: Date;
  createdTo?: Date;
}

// Paged Response interface matching backend PagedResponse
export interface PagedResponse<T> {
  items: T[];
  totalItems: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
}

// API Response interface matching backend Response model
export interface ApiResponse<T = any> {
  isSuccess: boolean;
  message: string;
  data?: T;
  errors?: any;
}

export interface UserManagementResponse extends ApiResponse<UserDetails> {}
export interface UsersPagedResponse
  extends ApiResponse<PagedResponse<UserDetails>> {}

export interface AddUserRequest {
  email: string;
  fullName: string;
  password: string;
  isActive: boolean;
  phoneNumber?: string;
  description?: string;
  profileImage?: File;
  profileImageUrl?: string;
  roles: string[];
}

export interface UpdateUserRequest {
  id?: string;
  fullName?: string;
  email?: string;
  isActive: boolean;
  phoneNumber?: string;
  description?: string;
  roles: string[];
}

export interface UserActionPermissions {
  canView: boolean;
  canEdit: boolean;
  canToggleStatus: boolean;
}

// Sort configuration
export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}
