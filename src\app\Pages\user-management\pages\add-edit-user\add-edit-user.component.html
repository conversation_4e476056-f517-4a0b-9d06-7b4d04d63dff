<div class="container-fluid py-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <p-card styleClass="shadow-sm">
        <!-- Header -->
        <ng-template pTemplate="header">
          <div
            class="d-flex justify-content-between align-items-center p-3 border-bottom"
          >
            <div>
              <h2 class="mb-1 fw-semibold text-dark">
                {{ isEditMode ? "Edit User" : "Add New User" }}
              </h2>
              <p class="text-muted mb-0">
                {{
                  isEditMode
                    ? "Update user information"
                    : "Create a new user account"
                }}
              </p>
            </div>
            <p-button
              icon="pi pi-times"
              styleClass="p-button-text p-button-rounded"
              (onClick)="cancel()"
            >
            </p-button>
          </div>
        </ng-template>

        <!-- Loading State -->
        <div *ngIf="isLoading" class="text-center py-5">
          <p-progressSpinner styleClass="w-4rem h-4rem"></p-progressSpinner>
          <p class="mt-3 text-muted">Loading user data...</p>
        </div>

        <!-- Form -->
        <form [formGroup]="userForm" (ngSubmit)="submit()" *ngIf="!isLoading">
          <div class="row">
            <!-- Full Name -->
            <div class="col-md-6 mb-4">
              <label for="fullName" class="form-label fw-medium">
                Full Name <span class="text-danger">*</span>
              </label>
              <input
                type="text"
                id="fullName"
                pInputText
                formControlName="fullName"
                placeholder="Enter full name"
                class="w-100"
                [class.ng-invalid]="isFieldInvalid('fullName')"
                [class.ng-dirty]="userForm.get('fullName')?.dirty"
              />
              <small *ngIf="isFieldInvalid('fullName')" class="text-danger">
                {{ getFieldError("fullName") }}
              </small>
            </div>

            <!-- Email -->
            <div class="col-md-6 mb-4">
              <label for="email" class="form-label fw-medium">
                Email Address <span class="text-danger">*</span>
              </label>
              <input
                type="email"
                id="email"
                pInputText
                formControlName="email"
                placeholder="Enter email address"
                class="w-100"
                [class.ng-invalid]="isFieldInvalid('email')"
                [class.ng-dirty]="userForm.get('email')?.dirty"
              />
              <small *ngIf="isFieldInvalid('email')" class="text-danger">
                {{ getFieldError("email") }}
              </small>
            </div>

            <!-- Phone Number -->
            <div class="col-md-6 mb-4">
              <label for="phoneNumber" class="form-label fw-medium">
                Phone Number
              </label>
              <input
                type="tel"
                id="phoneNumber"
                pInputText
                formControlName="phoneNumber"
                placeholder="Enter phone number"
                class="w-100"
                [class.ng-invalid]="isFieldInvalid('phoneNumber')"
                [class.ng-dirty]="userForm.get('phoneNumber')?.dirty"
              />
              <small *ngIf="isFieldInvalid('phoneNumber')" class="text-danger">
                {{ getFieldError("phoneNumber") }}
              </small>
            </div>

            <!-- Role -->
            <div class="col-md-6 mb-4">
              <label for="role" class="form-label fw-medium">
                Role <span class="text-danger">*</span>
              </label>
              <p-dropdown
                [options]="roleOptions"
                formControlName="role"
                placeholder="Select role"
                optionLabel="label"
                optionValue="value"
                styleClass="w-100"
                [class.ng-invalid]="isFieldInvalid('role')"
                [class.ng-dirty]="userForm.get('role')?.dirty"
              >
              </p-dropdown>
              <small *ngIf="isFieldInvalid('role')" class="text-danger">
                {{ getFieldError("role") }}
              </small>
            </div>

            <!-- Description -->
            <div class="col-12 mb-4">
              <label for="description" class="form-label fw-medium">
                Description
              </label>
              <textarea
                id="description"
                pInputTextarea
                formControlName="description"
                placeholder="Enter user description (optional)"
                rows="4"
                class="w-100"
              >
              </textarea>
            </div>

            <!-- Status (only for edit mode) -->
            <div class="col-12 mb-4" *ngIf="isEditMode">
              <div class="form-check">
                <input
                  type="checkbox"
                  id="isActive"
                  class="form-check-input"
                  formControlName="isActive"
                />
                <label for="isActive" class="form-check-label fw-medium">
                  Active User
                </label>
                <small class="form-text text-muted d-block">
                  Inactive users cannot access the system
                </small>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-content-end gap-3 pt-3 border-top">
            <p-button
              label="Cancel"
              styleClass="p-button-outlined"
              (onClick)="cancel()"
              [disabled]="isLoading"
            >
            </p-button>
            <p-button
              [label]="submitButtonText"
              type="submit"
              styleClass="p-button-primary"
              [loading]="isLoading"
              [disabled]="userForm.invalid || isLoading"
            >
            </p-button>
          </div>
        </form>
      </p-card>
    </div>
  </div>
</div>

<p-toast></p-toast>
