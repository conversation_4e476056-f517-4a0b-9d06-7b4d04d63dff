import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-pagination',
  standalone: false,
  templateUrl: './pagination.component.html',
  styleUrl: './pagination.component.scss',
})
export class PaginationComponent {
  @Input() totalRecords: number = 0;
  @Input() rows: number = 10;
  @Output() onPageChange = new EventEmitter<any>();

  handlePageChange(event: any) {
    this.onPageChange.emit(event);
  }
}
