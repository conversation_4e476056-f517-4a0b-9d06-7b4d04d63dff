.user-management-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding: 1.5rem;

  .header-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.5rem;

      .header-title h1 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .add-user-btn {
        background-color: #7c3aed;
        color: white;
        border: none;
        border-radius: 0.375rem;
        padding: 0.5rem 1rem;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: background-color 0.2s;

        &:hover {
          background-color: #6d28d9;
        }

        i {
          font-size: 0.875rem;
        }
      }
    }

    .filters-section {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .search-container {
        position: relative;
        flex: 1;
        max-width: 24rem;

        .search-icon {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          color: #9ca3af;
          font-size: 0.875rem;
        }

        .search-input {
          width: 100%;
          padding: 0.5rem 0.75rem 0.5rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;

          &:focus {
            outline: none;
            border-color: #7c3aed;
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
          }

          &::placeholder {
            color: #9ca3af;
          }
        }
      }

      .filter-controls {
        display: flex;
        gap: 1rem;

        .filter-select {
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          background: white;
          min-width: 8rem;

          &:focus {
            outline: none;
            border-color: #7c3aed;
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
          }
        }
      }
    }

    .table-container {
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      overflow: hidden;

      .table-wrapper {
        overflow-x: auto;
      }

      .users-table {
        width: 100%;
        border-collapse: collapse;

        .table-header {
          background-color: #f9fafb;

          th {
            padding: 0.75rem 1rem;
            text-align: left;
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
            border-bottom: 1px solid #e5e7eb;
          }
        }

        .table-row {
          &:hover {
            background-color: #f9fafb;
          }

          td {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
            font-size: 0.875rem;
          }

          .user-cell {
            .user-info {
              display: flex;
              align-items: center;
              gap: 0.75rem;

              .user-avatar {
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                background-color: #e5e7eb;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.75rem;
                font-weight: 500;
                color: #374151;
              }

              .user-avatar-img {
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                object-fit: cover;
              }

              .user-name {
                font-weight: 500;
                color: #1f2937;
              }
            }
          }

          .email-cell {
            color: #6b7280;
          }

          .role-cell {
            .role-badge {
              padding: 0.25rem 0.5rem;
              border-radius: 0.25rem;
              font-size: 0.75rem;
              font-weight: 500;

              &.role-admin {
                background-color: #dbeafe;
                color: #1e40af;
              }

              &.role-super-admin {
                background-color: #e9d5ff;
                color: #7c2d12;
              }

              &.role-user {
                background-color: #f3f4f6;
                color: #374151;
              }
            }
          }

          .status-cell {
            .status-badge {
              padding: 0.25rem 0.5rem;
              border-radius: 0.25rem;
              font-size: 0.75rem;
              font-weight: 500;

              &.status-active {
                background-color: #dcfce7;
                color: #166534;
              }

              &.status-inactive {
                background-color: #fee2e2;
                color: #991b1b;
              }
            }
          }

          .date-cell {
            color: #6b7280;
          }

          .actions-cell {
            .action-buttons {
              display: flex;
              gap: 0.5rem;

              .action-btn {
                width: 2rem;
                height: 2rem;
                border: none;
                background: none;
                cursor: pointer;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #6b7280;
                transition: all 0.2s;

                &:hover {
                  background-color: #f3f4f6;
                  color: #374151;
                }

                i {
                  font-size: 0.875rem;
                }
              }
            }
          }
        }
      }

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        color: #6b7280;

        .loading-spinner {
          width: 2rem;
          height: 2rem;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #7c3aed;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }
      }
    }

    .pagination-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 1.5rem;

      .pagination-info {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .pagination-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .pagination-btn {
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          background: white;
          color: #374151;
          border-radius: 0.375rem;
          cursor: pointer;
          font-size: 0.875rem;
          display: flex;
          align-items: center;
          gap: 0.25rem;
          transition: all 0.2s;

          &:hover:not(:disabled) {
            background-color: #f9fafb;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          i {
            font-size: 0.75rem;
          }
        }

        .pagination-number {
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          background: white;
          color: #374151;
          border-radius: 0.375rem;
          cursor: pointer;
          font-size: 0.875rem;
          min-width: 2rem;
          text-align: center;

          &.active {
            background-color: #7c3aed;
            color: white;
            border-color: #7c3aed;
          }
        }
      }
    }
  }

  .notification-bar {
    background-color: #1f2937;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;

    .notification-actions {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .learn-more-btn {
        background: none;
        border: none;
        color: #60a5fa;
        cursor: pointer;
        font-size: 0.875rem;

        &:hover {
          color: #93c5fd;
        }
      }

      .close-btn {
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        font-size: 1.25rem;
        line-height: 1;

        &:hover {
          color: #d1d5db;
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .user-management-container {
    padding: 1rem;

    .header-card {
      .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }

      .filters-section {
        flex-direction: column;
        gap: 1rem;

        .search-container {
          max-width: none;
        }

        .filter-controls {
          flex-direction: column;
          gap: 0.5rem;

          .filter-select {
            min-width: auto;
          }
        }
      }

      .pagination-container {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
      }
    }

    .notification-bar {
      flex-direction: column;
      gap: 0.75rem;
      text-align: center;
    }
  }
}
