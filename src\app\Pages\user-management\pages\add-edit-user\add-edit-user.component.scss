.container-fluid {
  background-color: #f8fafc;
  min-height: 100vh;
}

.p-card {
  border: none;
  border-radius: 0.5rem;

  .p-card-body {
    padding: 0;
  }
}

.form-label {
  color: #374151;
  margin-bottom: 0.5rem;
}

.p-inputtext,
.p-dropdown,
.p-inputtextarea {
  border-color: #d1d5db;

  &:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
  }

  &.ng-invalid.ng-dirty {
    border-color: #dc3545;

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
}

.p-button-primary {
  background-color: #7c3aed;
  border-color: #7c3aed;

  &:hover {
    background-color: #6d28d9;
    border-color: #6d28d9;
  }

  &:disabled {
    background-color: #9ca3af;
    border-color: #9ca3af;
  }
}

.p-button-outlined {
  color: #6b7280;
  border-color: #d1d5db;

  &:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
  }
}

.form-check-input:checked {
  background-color: #7c3aed;
  border-color: #7c3aed;
}

.text-danger {
  color: #dc3545 !important;
}

.text-muted {
  color: #6b7280 !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .d-flex.gap-3 {
    flex-direction: column;

    .p-button {
      width: 100%;
    }
  }
}
