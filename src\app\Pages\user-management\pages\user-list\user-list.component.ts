import { Component, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { NotificationService } from '../../../../core/Services';
import { UserManagementService } from '../../services/UserManagement.service';
import { Router } from '@angular/router';
import { Subject, takeUntil, finalize, debounceTime } from 'rxjs';
import {
  SortConfig,
  UserDetails,
  PaginationParameters,
} from '../../Models/UserManagement';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';

@Component({
  selector: 'app-user-list',
  standalone: false,
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.scss',
})
export class UserListComponent implements OnInit {
  Math = Math;
  users: UserDetails[] = [];
  error: string | null = null;
  isLoading: boolean = false;
  showFilters: boolean = false;
  filterForm: FormGroup;
  hasAdminAccess = false;

  // Pagination
  currentPage: number = 1;
  pageSize: number = 10;
  totalItems: number = 0;
  totalPages: number = 0;

  sortConfig: SortConfig = {
    field: 'createdOn',
    direction: 'desc',
  };

  roleOptions: { name: string; value: string }[] = [
    { name: 'All', value: 'All' },
    { name: 'Admin', value: 'Admin' },
    { name: 'User', value: 'User' },
    { name: 'Super Admin', value: 'Super Admin' },
  ];

  statusOptions: { name: string; value: string }[] = [
    { name: 'All', value: 'All' },
    { name: 'Active', value: 'active' },
    { name: 'Inactive', value: 'inactive' },
  ];

  // headerActions: CardHeaderAction[] = [];

  constructor(
    private userManagementService: UserManagementService,
    private notificationService: NotificationService,
    private router: Router,
    private fb: FormBuilder,
    private confirmationService: ConfirmationService,
  ) {
    this.filterForm = this.fb.group({
      name: [''],
      role: [''],
      status: [''],
    });

    this.filterForm.valueChanges.pipe(debounceTime(300)).subscribe(() => {
      this.currentPage = 1;
      this.loadUsers();
    });

    // this.setupHeaderActions();
  }

  ngOnInit(): void {
    this.loadUsers();
  }

  private loadUsers(): void {
    this.isLoading = true;

    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      sortField: this.sortConfig.field,
      sortOrder: this.sortConfig.direction,
      filters: this.filterForm.value,
    };

    this.userManagementService
      .getUsers(params)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (Response) => {
          this.users = Response.items;
          this.totalItems = Response.totalItems;
          this.totalPages = Response.totalPages;
        },

        error: (error) => {
          this.notificationService.showError(`${error.message}`);
        },
      });
  }

  handlePageChange(event: any): void {
    const pageNumber = Math.floor(event.first / event.rows) + 1;
    this.currentPage = pageNumber;
    this.loadUsers();
  }

  onPageChange(event: any): void {
    this.currentPage = event.page + 1;
    this.pageSize = event.rows;
    this.loadUsers();
  }
  // onPageChange(page: number): void {
  //   if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
  //     this.currentPage = page;
  //     this.loadUsers();
  //   }
  // }

  onSortChange(event: any): void {
    this.sortConfig = {
      field: event.field,
      direction: event.order === 1 ? 'asc' : 'desc',
    };
    this.currentPage = 1;
    this.loadUsers();
  }

  onViewUser(user: UserDetails): void {
    this.router.navigate(['/user-management/details', user.id]);
  }

  onEditUser(user: UserDetails): void {
    this.router.navigate(['/user-management/edit', user.id]);
  }

  toggleUserStatus(user: UserDetails): void {
    const newStatus = !user.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    this.confirmationService.confirm({
      message: `Are you sure you want to ${action} ${user.fullName}?`,
      header: 'Confirm Action',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.userManagementService.DeleteUser(user.id!, newStatus).subscribe({
          next: () => {
            this.notificationService.showSuccess(
              `User ${action}d successfully`,
            );
            this.loadUsers();
          },
          error: () => {
            this.notificationService.showError(`Failed to ${action} user`);
          },
        });
      },
    });
  }

  addUser(): void {
    this.router.navigate(['/user-management/add']);
  }

  refreshUsers(): void {
    this.loadUsers();
  }

  viewUser(user: UserDetails): void {
    this.onViewUser(user);
  }

  editUser(user: UserDetails): void {
    this.onEditUser(user);
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onFilterChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  // resetFilters(): void {
  //   this.filterForm.reset();
  //   this.sortConfig = {
  //     field: 'createdOn',
  //     direction: 'desc',
  //   };
  //   this.resetPaginationAndLoad();
  // }

  getProfilePictureUrl(user: UserDetails): string | undefined {
    if (!user.profileImageUrl) {
      return undefined;
    }

    if (user.profileImageUrl.startsWith('http')) {
      return user.profileImageUrl;
    }

    return user.profileImageUrl;
  }

  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  getRoleSeverity(role: string): string {
    switch (role) {
      case 'Admin':
        return 'info';
      case 'Super Admin':
        return 'warning';
      case 'User':
        return 'secondary';
      default:
        return 'secondary';
    }
  }

  getStatusSeverity(isActive: boolean): string {
    return isActive ? 'success' : 'danger';
  }

  // private setupHeaderActions(): void {
  //   this.headerActions = [
  //     {
  //       label: 'Add User',
  //       icon: 'pi-user-plus',
  //       action: 'add-user',
  //       variant: 'primary',
  //     },
  //   ];
  // }

  // onHeaderAction(action: string): void {
  //   switch (action) {
  //     case 'add-user':
  //       this.addUser();
  //       break;
  //   }
  // }
}
