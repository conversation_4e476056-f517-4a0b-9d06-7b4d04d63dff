import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';
import {
  UserDetails,
  PaginationParameters,
  PagedResponse,
  ApiResponse,
  AddUserRequest,
  UpdateUserRequest,
  UserManagementResponse,
  UsersPagedResponse,
} from '../Models/UserManagement';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UserManagementService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  private _users$ = new BehaviorSubject<PagedResponse<UserDetails> | null>(
    null,
  );

  public readonly users$ = this._users$.asObservable();

  constructor(private httpClient: HttpClient) {}

  getUsers(
    parameters: PaginationParameters,
  ): Observable<PagedResponse<UserDetails>> {
    let params = new HttpParams()
      .set('pageNumber', parameters.pageNumber.toString())
      .set('pageSize', parameters.pageSize.toString());

    if (parameters.sortField) {
      params = params.set('sortField', parameters.sortField);
    }
    if (parameters.sortOrder) {
      params = params.set('sortOrder', parameters.sortOrder);
    }
    if (parameters.name) {
      params = params.set('name', parameters.name);
    }
    if (parameters.role) {
      params = params.set('role', parameters.role);
    }
    if (parameters.status) {
      params = params.set('status', parameters.status);
    }

    return this.httpClient
      .get<UsersPagedResponse>(`${this.API_URL}/GetUsers`, { params })
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to fetch users');
          }
        }),
        tap((pagedResponse) => {
          this._users$.next(pagedResponse);
        }),
      );
  }

  getUserById(userId: string): Observable<UserDetails> {
    return this.httpClient
      .get<UserManagementResponse>(`${this.API_URL}/GetUserById/${userId}`)
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to fetch user');
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  addUser(userData: AddUserRequest): Observable<ApiResponse> {
    const formData = this.createFormData(userData);

    return this.httpClient
      .post<ApiResponse>(`${this.API_URL}/AddUser`, formData)
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to add user');
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  updateUser(
    userId: string,
    userData: UpdateUserRequest,
  ): Observable<ApiResponse> {
    return this.httpClient
      .put<ApiResponse>(`${this.API_URL}/UpdateUser/${userId}`, userData)
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to update user');
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  DeleteUser(userId: string, isActive: boolean): Observable<ApiResponse> {
    return this.httpClient
      .put<ApiResponse>(`${this.API_URL}/ToggleUserStatus/${userId}`, {
        isActive,
      })
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to delete user');
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  getAllUsers(): Observable<UserDetails[]> {
    return this.httpClient
      .get<ApiResponse<UserDetails[]>>(`${this.API_URL}/GetAll`)
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to fetch all users');
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  private createFormData(userData: AddUserRequest): FormData {
    const formData = new FormData();

    formData.append('email', userData.email);
    formData.append('fullName', userData.fullName);
    formData.append('password', userData.password);
    formData.append('isActive', userData.isActive.toString());

    if (userData.phoneNumber) {
      formData.append('phoneNumber', userData.phoneNumber);
    }
    if (userData.description) {
      formData.append('description', userData.description);
    }
    if (userData.profileImage) {
      formData.append('profileImage', userData.profileImage);
    }

    if (userData.roles && userData.roles.length > 0) {
      userData.roles.forEach((role, index) => {
        formData.append(`roles[${index}]`, role);
      });
    }

    return formData;
  }
}
