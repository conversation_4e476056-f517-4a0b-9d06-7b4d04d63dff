<div class="user-management-container">
  <!-- Header Section -->
  <div class="header-card">
    <div class="header-content">
      <div class="header-title">
        <h1>User Management</h1>
      </div>
      <button class="add-user-btn" (click)="addUser()" type="button">
        <i class="pi pi-plus"></i>
        Add User
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="filters-section" [formGroup]="filterForm">
      <div class="search-container">
        <i class="pi pi-search search-icon"></i>
        <input
          type="text"
          placeholder="Search users..."
          formControlName="name"
          class="search-input"
          (input)="onSearchChange()"
        />
      </div>

      <div class="filter-controls">
        <select
          formControlName="status"
          class="filter-select"
          (change)="onFilterChange()"
        >
          <option value="">Status</option>
          <option *ngFor="let status of statusOptions" [value]="status.value">
            {{ status.name }}
          </option>
        </select>

        <select
          formControlName="role"
          class="filter-select"
          (change)="onFilterChange()"
        >
          <option value="">Roles</option>
          <option *ngFor="let role of roleOptions" [value]="role.value">
            {{ role.name }}
          </option>
        </select>
      </div>
    </div>

    <!-- Data Table -->
    <div class="table-container">
      <div class="table-wrapper" *ngIf="!isLoading; else loadingTemplate">
        <table class="users-table">
          <thead>
            <tr class="table-header">
              <th>User</th>
              <th>Email</th>
              <th>Roles</th>
              <th>Status</th>
              <th>Created on</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of users" class="table-row">
              <td class="user-cell">
                <div class="user-info">
                  <div class="user-avatar" *ngIf="!getProfilePictureUrl(user)">
                    {{ getUserInitials(user) }}
                  </div>
                  <img
                    *ngIf="getProfilePictureUrl(user)"
                    [src]="getProfilePictureUrl(user)"
                    [alt]="user.fullName"
                    class="user-avatar-img"
                  />
                  <span class="user-name">{{
                    user.fullName || user.email
                  }}</span>
                </div>
              </td>
              <td class="email-cell">{{ user.email }}</td>
              <td class="role-cell">
                <span
                  class="role-badge"
                  [ngClass]="{
                    'role-admin': user.role === 'Admin',
                    'role-super-admin': user.role === 'Super Admin',
                    'role-user': user.role === 'User',
                  }"
                >
                  {{ user.role }}
                </span>
              </td>
              <td class="status-cell">
                <span
                  class="status-badge"
                  [ngClass]="{
                    'status-active': user.isActive,
                    'status-inactive': !user.isActive,
                  }"
                >
                  {{ user.isActive ? "Active" : "Inactive" }}
                </span>
              </td>
              <td class="date-cell">
                {{ user.createdOn | date: "MMM dd, yyyy" }}
              </td>
              <td class="actions-cell">
                <div class="action-buttons">
                  <button
                    class="action-btn view-btn"
                    (click)="viewUser(user)"
                    title="View User"
                  >
                    <i class="pi pi-eye"></i>
                  </button>
                  <button
                    class="action-btn edit-btn"
                    (click)="editUser(user)"
                    title="Edit User"
                  >
                    <i class="pi pi-pencil"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <ng-template #loadingTemplate>
        <div class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading users...</p>
        </div>
      </ng-template>
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span
          >Showing {{ (currentPage - 1) * pageSize + 1 }} to
          {{ Math.min(currentPage * pageSize, totalItems) }} of
          {{ totalItems }} entries</span
        >
      </div>
      <div class="pagination-controls">
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="onPageChange(currentPage - 1)"
        >
          <i class="pi pi-chevron-left"></i>
          Previous
        </button>

        <button
          class="pagination-number active"
          [class.active]="currentPage === 1"
        >
          {{ currentPage }}
        </button>

        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="onPageChange(currentPage + 1)"
        >
          Next
          <i class="pi pi-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Hardware Acceleration Notification -->
  <div class="notification-bar">
    <span>Improve performance by enabling hardware acceleration</span>
    <div class="notification-actions">
      <button class="learn-more-btn">Learn more</button>
      <button class="close-btn">×</button>
    </div>
  </div>
</div>
